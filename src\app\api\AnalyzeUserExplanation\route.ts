import { NextRequest, NextResponse } from 'next/server';
import { GoogleGenerativeAI } from '@google/generative-ai';

const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY!);

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      question, 
      choiceA, 
      choiceB, 
      choiceC, 
      choiceD, 
      correctAnswer, 
      userExplanation,
      topic,
      certificateName 
    } = body;

    if (!question || !userExplanation || !correctAnswer) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      );
    }

    const model = genAI.getGenerativeModel({ model: "gemini-2.5-pro" });

    const prompt = `
You are an expert ${certificateName || 'certification'} instructor analyzing a student's explanation for why they believe their answer is correct.

QUESTION: ${question}

CHOICES:
A) ${choiceA}
B) ${choiceB}
C) ${choiceC}
D) ${choiceD}

CORRECT ANSWER: ${correctAnswer}
TOPIC: ${topic || 'General'}

STUDENT'S EXPLANATION: ${userExplanation}

Please analyze the student's explanation and provide a comprehensive assessment. Return your response as a JSON object with the following structure:

{
  "isCorrect": boolean, // true if the student's reasoning demonstrates understanding of the correct answer
  "confidenceLevel": "high" | "medium" | "low", // confidence in the assessment
  "analysis": {
    "strengths": ["list of correct points in the explanation"],
    "weaknesses": ["list of incorrect or missing points"],
    "misconceptions": ["any misconceptions identified"],
    "keyConceptsUnderstood": ["concepts the student clearly understands"],
    "keyConceptsMissing": ["important concepts not mentioned or misunderstood"]
  },
  "feedback": {
    "positive": "What the student did well",
    "constructive": "Areas for improvement",
    "suggestions": ["specific study suggestions based on the analysis"]
  },
  "score": number, // 0-100 score based on understanding demonstrated
  "nextSteps": ["recommended actions for the student"],
  "relatedTopics": ["topics the student should review based on this analysis"]
}

Focus on:
1. Whether the student understands the underlying concepts
2. The logical flow of their reasoning
3. Accuracy of their understanding
4. Completeness of their explanation
5. Any gaps in knowledge that need addressing

Be encouraging but honest in your assessment. The goal is to help the student learn and improve.
`;

    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();

    // Try to parse the JSON response
    let analysisData;
    try {
      // Extract JSON from the response (in case there's extra text)
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        analysisData = JSON.parse(jsonMatch[0]);
      } else {
        throw new Error('No JSON found in response');
      }
    } catch (parseError) {
      console.error('Error parsing Gemini response:', parseError);
      console.log('Raw response:', text);
      
      // Fallback response if parsing fails
      analysisData = {
        isCorrect: false,
        confidenceLevel: "low",
        analysis: {
          strengths: [],
          weaknesses: ["Unable to analyze explanation due to parsing error"],
          misconceptions: [],
          keyConceptsUnderstood: [],
          keyConceptsMissing: []
        },
        feedback: {
          positive: "Thank you for providing an explanation.",
          constructive: "Please try rephrasing your explanation more clearly.",
          suggestions: ["Review the topic materials", "Practice explaining concepts step by step"]
        },
        score: 0,
        nextSteps: ["Review the correct answer and explanation"],
        relatedTopics: [topic || "General concepts"]
      };
    }

    return NextResponse.json({
      success: true,
      data: analysisData
    });

  } catch (error) {
    console.error('Error in AnalyzeUserExplanation API:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to analyze explanation',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
