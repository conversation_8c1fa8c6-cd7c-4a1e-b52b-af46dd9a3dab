"use client";

import React, { useState, useEffect, useCallback } from "react";
import { Certificate } from "@/Firebase/firestore/services/CertificatesService";
import { getCertificateQuestions, Question } from "@/Firebase/firestore/services/QuestionsService";
import {
  createAnswerFullBankAttempt,
  addQuestionsToAnswerFullBankAttempt,
  submitAnswerFullBankAnswer,
  updateAnswerFullBankProgress,
  completeAnswerFullBankAttempt,
  getCurrentAnswerFullBankAttempt,
  getAnswerFullBankQuestions,
  abandonAnswerFullBankAttempt,
  AnswerFullBankAttempt
} from "@/Firebase/firestore/services/AnswerFullBankService";
import { Timestamp } from "firebase/firestore";
import { useAuth } from "@/context";
import { useToast } from "@/components/ui/use-toast";
import {
  ArrowLeft,
  ChevronLeft,
  ChevronRight,
  BookOpen,
  CheckCircle,
  XCircle,
  Clock,
  Loader2,
  Lightbulb,
  ChevronDown,
  ChevronUp
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

interface AnswerFullQuestionBankProps {
  certificate: Certificate;
  onBack: () => void;
}

interface QuestionState {
  id: string;
  answered: boolean;
  isCorrect: boolean | null;
  userAnswer: 'A' | 'B' | 'C' | 'D' | null;
  timeSpent: number;
}

interface ChoiceExplanation {
  choice: 'A' | 'B' | 'C' | 'D';
  explanation: string;
  isCorrect: boolean;
  reasoning: string;
}

interface QuestionExplanationData {
  explanation: string;
  choiceExplanations: ChoiceExplanation[];
  keyPoints: string[];
  relatedConcepts: string[];
  difficulty: 'Easy' | 'Medium' | 'Hard';
  studyTips: string[];
}

export default function AnswerFullQuestionBank({ certificate, onBack }: AnswerFullQuestionBankProps) {
  // Add error boundary for useAuth
  let user = null;
  let authError = false;

  try {
    const authContext = useAuth();
    user = authContext.user;
  } catch (error) {
    console.error('Auth context error:', error);
    authError = true;
    // If auth context fails, redirect back immediately
    setTimeout(() => {
      onBack();
    }, 100);
  }

  const { toast } = useToast();
  
  const [questions, setQuestions] = useState<Question[]>([]);
  const [questionStates, setQuestionStates] = useState<Record<string, QuestionState>>({});
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [currentAnswer, setCurrentAnswer] = useState<'A' | 'B' | 'C' | 'D' | null>(null);
  const [questionExplanation, setQuestionExplanation] = useState<QuestionExplanationData | null>(null);
  const [isLoadingExplanation, setIsLoadingExplanation] = useState(false);
  const [showAdditionalLearning, setShowAdditionalLearning] = useState(false);
  const [questionStartTime, setQuestionStartTime] = useState<number>(Date.now());
  const [currentAttempt, setCurrentAttempt] = useState<AnswerFullBankAttempt | null>(null);
  const [componentState, setComponentState] = useState<'loading' | 'continue_dialog' | 'active' | 'error'>('loading');

  const startFreshAttempt = useCallback(async () => {
    if (authError || !user?.uid || !certificate.id) return;

    try {
      const fetchedQuestions = await getCertificateQuestions(user.uid, certificate.id);

      if (fetchedQuestions.length === 0) {
        toast({
          title: "No Questions Available",
          description: "Please add questions to the question bank first.",
          variant: "destructive",
        });
        onBack();
        return;
      }

      setQuestions(fetchedQuestions);

      // Create AnswerFull Bank attempt
      const attemptId = await createAnswerFullBankAttempt(
        user.uid,
        certificate.id,
        fetchedQuestions.length
      );

      // Add questions to the attempt
      const questionsForAttempt = fetchedQuestions.map(q => {
        const questionData: {
          questionId: string;
          questionText: string;
          choiceA: string;
          choiceB: string;
          choiceC: string;
          choiceD: string;
          correctAnswer: 'A' | 'B' | 'C' | 'D';
          topic?: string;
          explanation?: string;
        } = {
          questionId: q.id!,
          questionText: q.question,
          choiceA: q.choiceA,
          choiceB: q.choiceB,
          choiceC: q.choiceC,
          choiceD: q.choiceD,
          correctAnswer: q.correctAnswer,
        };

        // Only add optional fields if they have values
        if (q.category) {
          questionData.topic = q.category;
        }
        if (q.explanation) {
          questionData.explanation = q.explanation;
        }

        return questionData;
      });

      await addQuestionsToAnswerFullBankAttempt(attemptId, questionsForAttempt);

      // Set the current attempt
      setCurrentAttempt({
        id: attemptId,
        userId: user.uid,
        certificateId: certificate.id,
        status: 'in_progress',
        startedAt: Timestamp.fromDate(new Date()),
        totalQuestions: fetchedQuestions.length,
        answeredQuestions: 0,
        correctAnswers: 0,
        incorrectAnswers: 0,
        totalTimeSpent: 0,
        currentQuestionIndex: 0,
        createdAt: Timestamp.fromDate(new Date()),
        updatedAt: Timestamp.fromDate(new Date())
      });

      // Initialize question states
      const initialStates: Record<string, QuestionState> = {};
      fetchedQuestions.forEach(question => {
        if (question.id) {
          initialStates[question.id] = {
            id: question.id,
            answered: false,
            isCorrect: null,
            userAnswer: null,
            timeSpent: 0
          };
        }
      });
      setQuestionStates(initialStates);
      setCurrentQuestionIndex(0);
      setQuestionStartTime(Date.now());
    } catch (error) {
      console.error('Error starting fresh attempt:', error);
      throw error;
    }
  }, [authError, user?.uid, certificate.id, toast, onBack]);

  const loadQuestions = useCallback(async () => {
    if (authError || !user?.uid || !certificate.id || componentState !== 'loading') return;

    try {
      setComponentState('loading');

      // Check for existing in-progress attempt
      const existingAttempt = await getCurrentAnswerFullBankAttempt(user.uid, certificate.id);

      if (existingAttempt) {
        setComponentState('continue_dialog');
        return;
      }

      // No existing progress, start fresh
      await startFreshAttempt();
      setComponentState('active');
    } catch (error) {
      console.error('Error loading questions:', error);
      setComponentState('error');
      toast({
        title: "Error",
        description: "Failed to load questions. Please try again.",
        variant: "destructive",
      });
      onBack();
    }
  }, [authError, user?.uid, certificate.id, componentState, toast, onBack, startFreshAttempt]);

  const continueExistingAttempt = async () => {
    if (authError || !user?.uid || !certificate.id) return;

    try {
      setComponentState('loading');

      // Get the existing attempt
      const existingAttempt = await getCurrentAnswerFullBankAttempt(user.uid, certificate.id);
      if (!existingAttempt?.id) {
        throw new Error('No existing attempt found');
      }

      // Get all questions for the certificate
      const fetchedQuestions = await getCertificateQuestions(user.uid, certificate.id);

      if (fetchedQuestions.length === 0) {
        toast({
          title: "No Questions Available",
          description: "Please add questions to the question bank first.",
          variant: "destructive",
        });
        onBack();
        return;
      }

      setQuestions(fetchedQuestions);

      // Get the answered questions from the attempt
      const attemptQuestions = await getAnswerFullBankQuestions(existingAttempt.id);

      // Restore question states from the attempt
      const restoredStates: Record<string, QuestionState> = {};
      fetchedQuestions.forEach(question => {
        if (question.id) {
          const attemptQuestion = attemptQuestions.find(aq => aq.questionId === question.id);

          restoredStates[question.id] = {
            id: question.id,
            answered: !!attemptQuestion?.userAnswer,
            isCorrect: attemptQuestion?.isCorrect ?? null,
            userAnswer: attemptQuestion?.userAnswer ?? null,
            timeSpent: attemptQuestion?.timeSpent ?? 0
          };
        }
      });

      setQuestionStates(restoredStates);
      setCurrentAttempt(existingAttempt);
      setCurrentQuestionIndex(existingAttempt.currentQuestionIndex);
      setQuestionStartTime(Date.now());
      setComponentState('active'); // Move to active state

      toast({
        title: "Progress Restored",
        description: `Continuing from question ${existingAttempt.currentQuestionIndex + 1}. You've answered ${existingAttempt.answeredQuestions}/${existingAttempt.totalQuestions} questions.`,
      });
    } catch (error) {
      console.error('Error continuing existing attempt:', error);
      toast({
        title: "Error",
        description: "Failed to restore progress. Starting fresh.",
        variant: "destructive",
      });
      await startFreshAttempt();
      setComponentState('active');
    }
  };

  const startNewAttempt = async () => {
    try {
      setComponentState('loading');

      // Abandon the existing attempt first
      if (certificate.id) {
        const existingAttempt = await getCurrentAnswerFullBankAttempt(user!.uid, certificate.id);
        if (existingAttempt?.id) {
          await abandonAnswerFullBankAttempt(existingAttempt.id);
        }
      }

      await startFreshAttempt();
      setComponentState('active');
    } catch (error) {
      console.error('Error starting new attempt:', error);
      toast({
        title: "Error",
        description: "Failed to start new attempt. Please try again.",
        variant: "destructive",
      });
      onBack();
    }
  };

  useEffect(() => {
    loadQuestions();
  }, [loadQuestions]);

  // Save progress when user navigates away
  useEffect(() => {
    const handleBeforeUnload = () => {
      if (currentAttempt?.id && currentQuestionIndex >= 0) {
        // Use sendBeacon for reliable data sending during page unload
        navigator.sendBeacon('/api/updateProgress', JSON.stringify({
          attemptId: currentAttempt.id,
          currentQuestionIndex
        }));
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [currentAttempt?.id, currentQuestionIndex]);

  const fetchQuestionExplanation = useCallback(async (question: Question) => {
    try {
      setIsLoadingExplanation(true);

      const response = await fetch('/api/QuestionExplanation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          question: question.question,
          choiceA: question.choiceA,
          choiceB: question.choiceB,
          choiceC: question.choiceC,
          choiceD: question.choiceD,
          topic: question.category || 'GDPR',
          certificateName: certificate.name
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to fetch explanation');
      }

      const result = await response.json();

      if (result.success && result.data) {
        setQuestionExplanation(result.data);
      } else {
        throw new Error('Invalid response format');
      }
    } catch (error) {
      console.error('Error fetching question explanation:', error);
      toast({
        title: "Explanation Error",
        description: "Could not load detailed explanation. Basic explanation will be shown.",
        variant: "destructive",
      });
    } finally {
      setIsLoadingExplanation(false);
    }
  }, [certificate.name, toast]);

  const handleSubmitAnswer = async () => {
    if (!currentAnswer || !questions[currentQuestionIndex]?.id || !currentAttempt?.id) return;

    const currentQuestion = questions[currentQuestionIndex];
    const timeSpent = Math.floor((Date.now() - questionStartTime) / 1000);

    try {
      // Submit answer to database and get if it's correct
      const isCorrect = await submitAnswerFullBankAnswer(
        currentAttempt.id,
        currentQuestion.id!,
        currentAnswer,
        timeSpent
      );

      // Update question state
      setQuestionStates(prev => ({
        ...prev,
        [currentQuestion.id!]: {
          ...prev[currentQuestion.id!],
          answered: true,
          isCorrect,
          userAnswer: currentAnswer,
          timeSpent
        }
      }));

      // Update progress in database
      await updateAnswerFullBankProgress(currentAttempt.id, currentQuestionIndex + 1);

      if (!isCorrect) {
        // Don't fetch explanation - let user try again without seeing the answer
        toast({
          title: "Incorrect Answer",
          description: "Try again! This question has been added to your repair center for review.",
          variant: "destructive",
        });
      } else {
        // For correct answers, just show a success message and move to next
        toast({
          title: "Correct!",
          description: "Well done! Moving to the next question.",
        });
        setTimeout(() => {
          handleNextQuestion();
        }, 1500);
      }
    } catch (error) {
      console.error('Error submitting answer:', error);
      toast({
        title: "Error",
        description: "Failed to submit answer. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleTryAgain = () => {
    const currentQuestion = questions[currentQuestionIndex];
    if (!currentQuestion?.id) return;

    // Reset the question state to allow re-answering
    setQuestionStates(prev => ({
      ...prev,
      [currentQuestion.id!]: {
        ...prev[currentQuestion.id!],
        answered: false,
        isCorrect: null,
        userAnswer: null,
        timeSpent: 0
      }
    }));

    // Clear current answer and explanation data
    setCurrentAnswer(null);
    setQuestionExplanation(null);
    setShowAdditionalLearning(false);
    setQuestionStartTime(Date.now());

    toast({
      title: "Try Again",
      description: "You can now re-answer this question.",
    });
  };

  const handleNextQuestion = async () => {
    if (currentQuestionIndex < questions.length - 1) {
      const newIndex = currentQuestionIndex + 1;
      setCurrentQuestionIndex(newIndex);
      setCurrentAnswer(null);
      setQuestionExplanation(null);
      setShowAdditionalLearning(false);
      setQuestionStartTime(Date.now());

      // Update progress in database
      if (currentAttempt?.id) {
        try {
          await updateAnswerFullBankProgress(currentAttempt.id, newIndex);
        } catch (error) {
          console.error('Error updating progress:', error);
        }
      }
    } else {
      // All questions completed
      await handleCompleteQuestionBank();
    }
  };

  const handleCompleteQuestionBank = async () => {
    if (!currentAttempt?.id) return;

    try {
      await completeAnswerFullBankAttempt(currentAttempt.id);

      const answeredCount = Object.values(questionStates).filter(state => state.answered).length;
      const correctCount = Object.values(questionStates).filter(state => state.isCorrect === true).length;

      toast({
        title: "Question Bank Completed!",
        description: `You answered ${correctCount}/${answeredCount} questions correctly. Incorrect answers have been added to your repair center.`,
      });

      // Go back to setup after a delay
      setTimeout(() => {
        onBack();
      }, 3000);
    } catch (error) {
      console.error('Error completing question bank:', error);
      toast({
        title: "Error",
        description: "Failed to complete question bank. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handlePreviousQuestion = async () => {
    if (currentQuestionIndex > 0) {
      const newIndex = currentQuestionIndex - 1;
      setCurrentQuestionIndex(newIndex);
      setCurrentAnswer(null);
      setQuestionExplanation(null);
      setShowAdditionalLearning(false);
      setQuestionStartTime(Date.now());

      // Update progress in database
      if (currentAttempt?.id) {
        try {
          await updateAnswerFullBankProgress(currentAttempt.id, newIndex);
        } catch (error) {
          console.error('Error updating progress:', error);
        }
      }
    }
  };

  const handleQuestionNavigation = async (index: number) => {
    setCurrentQuestionIndex(index);
    setCurrentAnswer(null);
    setQuestionExplanation(null);
    setShowAdditionalLearning(false);
    setQuestionStartTime(Date.now());

    // Update progress in database
    if (currentAttempt?.id) {
      try {
        await updateAnswerFullBankProgress(currentAttempt.id, index);
      } catch (error) {
        console.error('Error updating progress:', error);
      }
    }
  };

  const getQuestionStatusColor = (questionId: string) => {
    const state = questionStates[questionId];
    if (!state?.answered) return 'bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-400';
    // Don't show correct/incorrect colors - just show that it was answered
    return 'bg-blue-500 text-white';
  };

  const getNavigationRange = () => {
    const start = Math.max(0, currentQuestionIndex - 10);
    const end = Math.min(questions.length, currentQuestionIndex + 11);
    return { start, end };
  };

  if (componentState === 'loading') {
    return (
      <div className="max-w-7xl mx-auto px-8 py-8">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-purple-600 mx-auto mb-4" />
          <p className="text-gray-500 dark:text-gray-400">Loading question bank...</p>
        </div>
      </div>
    );
  }

  if (componentState === 'continue_dialog') {
    return (
      <div className="max-w-7xl mx-auto px-8 py-8">
        <div className="flex items-center justify-between mb-8">
          <Button variant="ghost" onClick={onBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Setup
          </Button>
        </div>

        <Card className="max-w-2xl mx-auto">
          <CardHeader className="text-center">
            <div className="bg-gradient-to-r from-purple-500 to-indigo-600 p-4 rounded-xl shadow-lg w-fit mx-auto mb-4">
              <BookOpen className="h-8 w-8 text-white" />
            </div>
            <CardTitle className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
              Continue Your Progress?
            </CardTitle>
            <p className="text-gray-600 dark:text-gray-400">
              You have an existing AnswerFull Question Bank session in progress
            </p>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
              <div className="flex items-center gap-3 mb-3">
                <Clock className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                <span className="font-semibold text-blue-800 dark:text-blue-300">Previous Session Found</span>
              </div>
              <p className="text-blue-700 dark:text-blue-300 text-sm">
                You can continue from where you left off or start a completely new session.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Button
                onClick={continueExistingAttempt}
                className="h-auto p-6 bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600"
              >
                <div className="text-center">
                  <CheckCircle className="h-6 w-6 mx-auto mb-2" />
                  <div className="font-semibold">Continue Progress</div>
                  <div className="text-sm opacity-90">Resume where you left off</div>
                </div>
              </Button>

              <Button
                onClick={startNewAttempt}
                variant="outline"
                className="h-auto p-6 border-2 hover:bg-gray-50 dark:hover:bg-gray-800"
              >
                <div className="text-center">
                  <BookOpen className="h-6 w-6 mx-auto mb-2" />
                  <div className="font-semibold">Start Fresh</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Begin a new session</div>
                </div>
              </Button>
            </div>

            <div className="text-center">
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Starting fresh will not delete your previous progress - it will remain in your history
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }



  const currentQuestion = questions[currentQuestionIndex];
  const currentState = questionStates[currentQuestion?.id || ''];
  const { start, end } = getNavigationRange();
  const answeredCount = Object.values(questionStates).filter(state => state.answered).length;
  const correctCount = Object.values(questionStates).filter(state => state.isCorrect === true).length;

  // Handle auth error
  if (authError) {
    return (
      <div className="max-w-7xl mx-auto px-8 py-8">
        <div className="text-center">
          <XCircle className="h-16 w-16 text-red-400 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
            Authentication Error
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            Please refresh the page or sign in again.
          </p>
          <Button onClick={onBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Setup
          </Button>
        </div>
      </div>
    );
  }

  // Debug logging
  console.log('AnswerFullQuestionBank render state:', {
    componentState,
    questionsLength: questions.length,
    currentQuestionIndex,
    currentQuestion: currentQuestion?.question?.substring(0, 50) + '...',
    currentAttemptId: currentAttempt?.id,
    userId: user?.uid
  });

  // Ensure we have questions and are in active state before rendering main content
  if (componentState !== 'active' || questions.length === 0) {
    if (componentState === 'active' && questions.length === 0) {
      return (
        <div className="max-w-7xl mx-auto px-8 py-8">
          <div className="text-center">
            <BookOpen className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
              No Questions Available
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              Please add questions to the question bank first.
            </p>
            <Button onClick={onBack}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Setup
            </Button>
          </div>
        </div>
      );
    }
    return null; // Let other conditions handle rendering
  }

  return (
    <div className="max-w-7xl mx-auto px-8 py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={onBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Setup
          </Button>
          <div className="flex items-center gap-3">
            <div className="bg-gradient-to-r from-purple-500 to-indigo-600 p-3 rounded-xl shadow-lg">
              <BookOpen className="h-6 w-6 text-white" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                AnswerFull Question Bank
              </h2>
              <p className="text-gray-600 dark:text-gray-400">
                Answer all questions in order with detailed explanations
              </p>
            </div>
          </div>
        </div>
        
        <div className="flex items-center gap-4">
          <Badge variant="outline" className="text-sm">
            Question {currentQuestionIndex + 1} of {questions.length}
          </Badge>
          <Badge variant="outline" className="text-sm">
            Progress: {answeredCount}/{questions.length}
          </Badge>
          <Badge variant="outline" className="text-sm">
            Correct: {correctCount}/{answeredCount || 1}
          </Badge>
        </div>
      </div>

      {/* Question Navigation Bar */}
      <Card className="mb-6">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg">Question Navigation</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between mb-4">
            <Button
              variant="outline"
              size="sm"
              onClick={handlePreviousQuestion}
              disabled={currentQuestionIndex === 0}
            >
              <ChevronLeft className="h-4 w-4 mr-1" />
              Previous
            </Button>

            <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
              <span>Showing questions {start + 1}-{end}</span>
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={handleNextQuestion}
              disabled={currentQuestionIndex === questions.length - 1}
            >
              Next
              <ChevronRight className="h-4 w-4 ml-1" />
            </Button>
          </div>

          <div className="flex flex-wrap gap-2">
            {questions.slice(start, end).map((question, index) => {
              const actualIndex = start + index;
              const questionId = question.id || '';
              return (
                <Button
                  key={questionId}
                  variant={actualIndex === currentQuestionIndex ? "default" : "outline"}
                  size="sm"
                  className={`w-10 h-10 p-0 ${getQuestionStatusColor(questionId)} ${
                    actualIndex === currentQuestionIndex ? 'ring-2 ring-purple-500' : ''
                  }`}
                  onClick={() => handleQuestionNavigation(actualIndex)}
                >
                  {actualIndex + 1}
                </Button>
              );
            })}
          </div>

          <div className="flex items-center gap-4 mt-4 text-sm">
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
              <span className="text-gray-600 dark:text-gray-400">Not answered</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-blue-500 rounded"></div>
              <span className="text-gray-600 dark:text-gray-400">Answered</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Current Question */}
      <Card className="mb-6">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-xl">
              Question {currentQuestionIndex + 1}
            </CardTitle>
            <div className="flex items-center gap-2">
              {currentQuestion.category && (
                <Badge variant="secondary">{currentQuestion.category}</Badge>
              )}
              {currentState?.answered && (
                <Badge variant="outline">Answered</Badge>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-lg font-medium mb-6 p-6 bg-gray-50 dark:bg-gray-800 rounded-lg">
            {currentQuestion.question}
          </div>

          <div className="grid grid-cols-1 gap-3 mb-6">
            {(['A', 'B', 'C', 'D'] as const).map((choice) => {
              const isSelected = currentAnswer === choice;

              let buttonClass = "text-left justify-start p-4 h-auto";

              // Only show selection highlighting, no previous answer indicators
              if (isSelected) {
                buttonClass += " bg-purple-100 dark:bg-purple-900/30 border-purple-500";
              }

              return (
                <Button
                  key={choice}
                  variant="outline"
                  className={buttonClass}
                  onClick={() => !currentState?.answered && setCurrentAnswer(choice)}
                  disabled={currentState?.answered}
                >
                  <span className="font-bold mr-3">{choice}.</span>
                  <span>{currentQuestion[`choice${choice}` as keyof Question] as string}</span>
                </Button>
              );
            })}
          </div>

          {!currentState?.answered && (
            <Button
              onClick={handleSubmitAnswer}
              className="w-full bg-purple-600 hover:bg-purple-700"
              disabled={!currentAnswer}
            >
              Submit Answer
            </Button>
          )}

          {currentState?.answered && currentState.isCorrect && (
            <div className="text-center">
              <div className="flex items-center justify-center gap-2 text-green-600 mb-4">
                <CheckCircle className="h-6 w-6" />
                <span className="text-lg font-semibold">Correct!</span>
              </div>
              <Button onClick={handleNextQuestion} className="bg-green-600 hover:bg-green-700">
                Next Question
                <ChevronRight className="h-4 w-4 ml-2" />
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Action buttons for incorrect answers */}
      {currentState?.answered && !currentState.isCorrect && (
        <Card className="mb-6 border-red-200 dark:border-red-800">
          <CardContent className="pt-6">
            <div className="text-center">
              <div className="flex items-center justify-center gap-2 text-red-600 mb-4">
                <XCircle className="h-6 w-6" />
                <span className="text-lg font-semibold">Incorrect Answer</span>
              </div>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Try again or continue to the next question.
              </p>
              <div className="flex gap-3 justify-center">
                <Button onClick={handleTryAgain} variant="outline" className="border-blue-500 text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Try Again
                </Button>
                <Button onClick={handleNextQuestion} className="bg-red-600 hover:bg-red-700">
                  Continue to Next Question
                  <ChevronRight className="h-4 w-4 ml-2" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
