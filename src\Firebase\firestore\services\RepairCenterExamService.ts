import {
  collection,
  doc,
  addDoc,
  getDocs,
  getDoc,
  updateDoc,
  query,
  where,
  orderBy,
  serverTimestamp,
  Timestamp,
  writeBatch
} from 'firebase/firestore';
import { firestore } from '../firestoreConfig';

export interface RepairCenterExamAttempt {
  id?: string;
  userId: string;
  certificateId: string;
  questionIds: string[];
  status: 'in_progress' | 'completed' | 'abandoned';
  startedAt: Timestamp;
  completedAt?: Timestamp;
  totalQuestions: number;
  answeredQuestions: number;
  correctAnswers: number;
  incorrectAnswers: number;
  totalTimeSpent: number;
  currentQuestionIndex: number;
  examType: 'repair_center_practice';
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface RepairCenterExamQuestion {
  id?: string;
  questionId: string;
  questionText: string;
  choiceA: string;
  choiceB: string;
  choiceC: string;
  choiceD: string;
  correctAnswer: 'A' | 'B' | 'C' | 'D';
  userAnswer?: 'A' | 'B' | 'C' | 'D';
  userExplanation?: string;
  explanationAnalysis?: {
    isCorrect: boolean;
    score: number;
    feedback: string;
    suggestions: string[];
  };
  isCorrect?: boolean;
  timeSpent?: number;
  topic?: string;
  explanation?: string;
  orderIndex: number;
  answeredAt?: Timestamp;
}

const REPAIR_EXAM_ATTEMPTS_COLLECTION = 'repairCenterExamAttempts';
const QUESTIONS_SUBCOLLECTION = 'questions';

/**
 * Create a new repair center exam attempt
 */
export const createRepairCenterExamAttempt = async (
  userId: string,
  certificateId: string,
  questionIds: string[]
): Promise<string> => {
  try {
    const attemptsRef = collection(firestore, REPAIR_EXAM_ATTEMPTS_COLLECTION);
    
    const newAttempt: Omit<RepairCenterExamAttempt, 'id'> = {
      userId,
      certificateId,
      questionIds,
      status: 'in_progress',
      startedAt: serverTimestamp() as Timestamp,
      totalQuestions: questionIds.length,
      answeredQuestions: 0,
      correctAnswers: 0,
      incorrectAnswers: 0,
      totalTimeSpent: 0,
      currentQuestionIndex: 0,
      examType: 'repair_center_practice',
      createdAt: serverTimestamp() as Timestamp,
      updatedAt: serverTimestamp() as Timestamp,
    };

    const docRef = await addDoc(attemptsRef, newAttempt);
    return docRef.id;
  } catch (error) {
    console.error('Error creating repair center exam attempt:', error);
    throw new Error('Failed to create repair center exam attempt');
  }
};

/**
 * Add questions to a repair center exam attempt
 */
export const addQuestionsToRepairCenterExamAttempt = async (
  attemptId: string,
  questions: Array<{
    questionId: string;
    questionText: string;
    choiceA: string;
    choiceB: string;
    choiceC: string;
    choiceD: string;
    correctAnswer: 'A' | 'B' | 'C' | 'D';
    topic?: string;
    explanation?: string;
  }>
): Promise<void> => {
  try {
    const attemptRef = doc(firestore, REPAIR_EXAM_ATTEMPTS_COLLECTION, attemptId);
    const questionsRef = collection(attemptRef, QUESTIONS_SUBCOLLECTION);
    
    const batch = writeBatch(firestore);
    
    questions.forEach((question, index) => {
      const questionDoc = doc(questionsRef);
      const questionData: Omit<RepairCenterExamQuestion, 'id'> = {
        ...question,
        orderIndex: index,
      };
      batch.set(questionDoc, questionData);
    });
    
    await batch.commit();
  } catch (error) {
    console.error('Error adding questions to repair center exam attempt:', error);
    throw new Error('Failed to add questions to repair center exam attempt');
  }
};

/**
 * Submit answer with explanation for a repair center exam question
 */
export const submitRepairCenterExamAnswer = async (
  attemptId: string,
  questionId: string,
  answer: 'A' | 'B' | 'C' | 'D',
  userExplanation: string,
  timeSpent: number
): Promise<{ isCorrect: boolean; explanationAnalysis?: any }> => {
  try {
    const attemptRef = doc(firestore, REPAIR_EXAM_ATTEMPTS_COLLECTION, attemptId);
    const questionsRef = collection(attemptRef, QUESTIONS_SUBCOLLECTION);
    const questionQuery = query(questionsRef, where('questionId', '==', questionId));
    const questionSnap = await getDocs(questionQuery);
    
    if (questionSnap.empty) {
      throw new Error('Question not found in exam attempt');
    }
    
    const questionDoc = questionSnap.docs[0];
    const questionData = questionDoc.data() as RepairCenterExamQuestion;
    const isCorrect = answer === questionData.correctAnswer;
    
    // Update question with answer and explanation
    await updateDoc(questionDoc.ref, {
      userAnswer: answer,
      userExplanation,
      isCorrect,
      timeSpent,
      answeredAt: serverTimestamp(),
    });
    
    // Update attempt statistics
    const attemptSnap = await getDoc(attemptRef);
    if (attemptSnap.exists()) {
      const attemptData = attemptSnap.data() as RepairCenterExamAttempt;
      const newAnsweredQuestions = attemptData.answeredQuestions + 1;
      const newCorrectAnswers = attemptData.correctAnswers + (isCorrect ? 1 : 0);
      const newIncorrectAnswers = attemptData.incorrectAnswers + (isCorrect ? 0 : 1);
      const newTotalTimeSpent = attemptData.totalTimeSpent + timeSpent;
      
      await updateDoc(attemptRef, {
        answeredQuestions: newAnsweredQuestions,
        correctAnswers: newCorrectAnswers,
        incorrectAnswers: newIncorrectAnswers,
        totalTimeSpent: newTotalTimeSpent,
        updatedAt: serverTimestamp(),
      });
    }
    
    return { isCorrect };
  } catch (error) {
    console.error('Error submitting repair center exam answer:', error);
    throw new Error('Failed to submit repair center exam answer');
  }
};

/**
 * Update explanation analysis for a question
 */
export const updateQuestionExplanationAnalysis = async (
  attemptId: string,
  questionId: string,
  analysis: any
): Promise<void> => {
  try {
    const attemptRef = doc(firestore, REPAIR_EXAM_ATTEMPTS_COLLECTION, attemptId);
    const questionsRef = collection(attemptRef, QUESTIONS_SUBCOLLECTION);
    const questionQuery = query(questionsRef, where('questionId', '==', questionId));
    const questionSnap = await getDocs(questionQuery);
    
    if (questionSnap.empty) {
      throw new Error('Question not found in exam attempt');
    }
    
    const questionDoc = questionSnap.docs[0];
    await updateDoc(questionDoc.ref, {
      explanationAnalysis: analysis,
      updatedAt: serverTimestamp(),
    });
  } catch (error) {
    console.error('Error updating explanation analysis:', error);
    throw new Error('Failed to update explanation analysis');
  }
};

/**
 * Get repair center exam attempt
 */
export const getRepairCenterExamAttempt = async (attemptId: string): Promise<RepairCenterExamAttempt | null> => {
  try {
    const attemptRef = doc(firestore, REPAIR_EXAM_ATTEMPTS_COLLECTION, attemptId);
    const attemptSnap = await getDoc(attemptRef);
    
    if (!attemptSnap.exists()) {
      return null;
    }
    
    return { id: attemptSnap.id, ...attemptSnap.data() } as RepairCenterExamAttempt;
  } catch (error) {
    console.error('Error getting repair center exam attempt:', error);
    throw new Error('Failed to get repair center exam attempt');
  }
};

/**
 * Get questions for a repair center exam attempt
 */
export const getRepairCenterExamQuestions = async (attemptId: string): Promise<RepairCenterExamQuestion[]> => {
  try {
    const attemptRef = doc(firestore, REPAIR_EXAM_ATTEMPTS_COLLECTION, attemptId);
    const questionsRef = collection(attemptRef, QUESTIONS_SUBCOLLECTION);
    const questionsQuery = query(questionsRef, orderBy('orderIndex', 'asc'));
    const questionsSnap = await getDocs(questionsQuery);
    
    return questionsSnap.docs.map(doc => ({ id: doc.id, ...doc.data() } as RepairCenterExamQuestion));
  } catch (error) {
    console.error('Error getting repair center exam questions:', error);
    throw new Error('Failed to get repair center exam questions');
  }
};

/**
 * Complete a repair center exam attempt
 */
export const completeRepairCenterExamAttempt = async (attemptId: string): Promise<RepairCenterExamAttempt> => {
  try {
    const attemptRef = doc(firestore, REPAIR_EXAM_ATTEMPTS_COLLECTION, attemptId);
    const attemptSnap = await getDoc(attemptRef);
    
    if (!attemptSnap.exists()) {
      throw new Error('Exam attempt not found');
    }
    
    const attemptData = attemptSnap.data() as RepairCenterExamAttempt;
    const percentage = attemptData.totalQuestions > 0 
      ? (attemptData.correctAnswers / attemptData.totalQuestions) * 100 
      : 0;
    
    await updateDoc(attemptRef, {
      status: 'completed',
      completedAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    });
    
    // Return updated attempt data
    const updatedSnap = await getDoc(attemptRef);
    return { id: updatedSnap.id, ...updatedSnap.data() } as RepairCenterExamAttempt;
  } catch (error) {
    console.error('Error completing repair center exam attempt:', error);
    throw new Error('Failed to complete repair center exam attempt');
  }
};

/**
 * Get user's repair center exam attempts
 */
export const getUserRepairCenterExamAttempts = async (
  userId: string,
  certificateId: string
): Promise<RepairCenterExamAttempt[]> => {
  try {
    const attemptsRef = collection(firestore, REPAIR_EXAM_ATTEMPTS_COLLECTION);
    const attemptsQuery = query(
      attemptsRef,
      where('userId', '==', userId),
      where('certificateId', '==', certificateId),
      orderBy('createdAt', 'desc')
    );
    const attemptsSnap = await getDocs(attemptsQuery);
    
    return attemptsSnap.docs.map(doc => ({ id: doc.id, ...doc.data() } as RepairCenterExamAttempt));
  } catch (error) {
    console.error('Error getting user repair center exam attempts:', error);
    throw new Error('Failed to get user repair center exam attempts');
  }
};
