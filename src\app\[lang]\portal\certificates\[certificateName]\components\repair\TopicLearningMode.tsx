"use client";

import React, { useState, useEffect } from "react";
import { Certificate } from "@/Firebase/firestore/services/CertificatesService";
import { useAuth } from "@/context";
import { useToast } from "@/components/ui/use-toast";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import {
  ArrowLeft,
  ChevronLeft,
  ChevronRight,
  Loader2,
  BookOpen,
  Brain,
  Target,
  CheckCircle,
  XCircle,
  Lightbulb,
  Clock
} from "lucide-react";

interface TopicLearningModeProps {
  certificate: Certificate;
  topics: string[];
  onBack: () => void;
}

interface TopicQuestion {
  id: string;
  question: string;
  topic: string;
  difficulty: 'easy' | 'medium' | 'hard';
  keyPoints: string[];
  commonMisconceptions: string[];
  evaluationCriteria: string[];
  sampleGoodAnswer: string;
  relatedConcepts: string[];
  timeEstimate: string;
}

interface QuestionSession {
  questions: TopicQuestion[];
  learningObjectives: string[];
  studyTips: string[];
  additionalResources: string[];
}

interface UserResponse {
  questionId: string;
  answer: string;
  analysis?: any;
  timeSpent: number;
}

export default function TopicLearningMode({ certificate, topics, onBack }: TopicLearningModeProps) {
  const [session, setSession] = useState<QuestionSession | null>(null);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [userAnswer, setUserAnswer] = useState('');
  const [responses, setResponses] = useState<UserResponse[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const [startTime, setStartTime] = useState<number>(Date.now());

  const { user } = useAuth();
  const { toast } = useToast();

  useEffect(() => {
    generateTopicQuestions();
  }, []);

  const generateTopicQuestions = async () => {
    try {
      setIsLoading(true);
      
      const response = await fetch('/api/GenerateTopicQuestions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          topics,
          certificateName: certificate.name,
          difficulty: 'medium',
          questionCount: Math.min(topics.length * 2, 8),
          focusAreas: []
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to generate questions');
      }

      const result = await response.json();
      
      if (result.success && result.data) {
        setSession(result.data);
        setStartTime(Date.now());
        
        toast({
          title: "Topic Learning Session Ready",
          description: `Generated ${result.data.questions.length} conceptual questions for your topics.`,
        });
      } else {
        throw new Error('Invalid response format');
      }
    } catch (error) {
      console.error('Error generating topic questions:', error);
      toast({
        title: "Error",
        description: "Failed to generate topic questions. Please try again.",
        variant: "destructive",
      });
      onBack();
    } finally {
      setIsLoading(false);
    }
  };

  const analyzeAnswer = async (question: TopicQuestion, answer: string) => {
    try {
      setIsAnalyzing(true);
      
      const response = await fetch('/api/AnalyzeUserExplanation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          question: question.question,
          choiceA: "N/A - Open ended question",
          choiceB: "N/A - Open ended question", 
          choiceC: "N/A - Open ended question",
          choiceD: "N/A - Open ended question",
          correctAnswer: "N/A",
          userExplanation: answer,
          topic: question.topic,
          certificateName: certificate.name
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to analyze answer');
      }

      const result = await response.json();
      
      if (result.success && result.data) {
        return result.data;
      } else {
        throw new Error('Invalid response format');
      }
    } catch (error) {
      console.error('Error analyzing answer:', error);
      toast({
        title: "Analysis Error",
        description: "Could not analyze your answer. Please try again.",
        variant: "destructive",
      });
      return null;
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleSubmitAnswer = async () => {
    if (!userAnswer.trim() || !session) {
      toast({
        title: "Incomplete Answer",
        description: "Please provide an answer before submitting.",
        variant: "destructive",
      });
      return;
    }

    const currentQuestion = session.questions[currentQuestionIndex];
    const timeSpent = Math.floor((Date.now() - startTime) / 1000);

    try {
      setIsSubmitting(true);

      // Analyze the answer
      const analysis = await analyzeAnswer(currentQuestion, userAnswer);

      // Save response
      const newResponse: UserResponse = {
        questionId: currentQuestion.id,
        answer: userAnswer,
        analysis,
        timeSpent
      };

      setResponses(prev => [...prev, newResponse]);

      // Show feedback
      if (analysis?.score >= 70) {
        toast({
          title: "Great Understanding!",
          description: `Score: ${analysis.score}/100. Your explanation shows good grasp of the concepts.`,
        });
      } else if (analysis?.score >= 50) {
        toast({
          title: "Good Progress",
          description: `Score: ${analysis.score}/100. Review the feedback to improve your understanding.`,
          variant: "default",
        });
      } else {
        toast({
          title: "Keep Learning",
          description: `Score: ${analysis?.score || 0}/100. Focus on the key concepts and try again.`,
          variant: "destructive",
        });
      }

      // Move to next question or show results
      setTimeout(() => {
        if (currentQuestionIndex < session.questions.length - 1) {
          handleNextQuestion();
        } else {
          setShowResults(true);
        }
      }, 2000);

    } catch (error) {
      console.error('Error submitting answer:', error);
      toast({
        title: "Error",
        description: "Failed to submit answer. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleNextQuestion = () => {
    if (currentQuestionIndex < (session?.questions.length || 0) - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
      setUserAnswer('');
      setStartTime(Date.now());
    }
  };

  const handlePreviousQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(currentQuestionIndex - 1);
      setUserAnswer('');
      setStartTime(Date.now());
    }
  };

  if (isLoading) {
    return (
      <div className="max-w-4xl mx-auto px-6 py-8">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-purple-600 mx-auto mb-4" />
          <p className="text-gray-500 dark:text-gray-400">Generating personalized topic questions...</p>
        </div>
      </div>
    );
  }

  if (!session) {
    return (
      <div className="max-w-4xl mx-auto px-6 py-8">
        <div className="text-center">
          <XCircle className="h-16 w-16 text-red-400 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
            Failed to Load Session
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            Unable to generate topic questions. Please try again.
          </p>
          <Button onClick={onBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Repair Center
          </Button>
        </div>
      </div>
    );
  }

  if (showResults) {
    const averageScore = responses.reduce((sum, r) => sum + (r.analysis?.score || 0), 0) / responses.length;
    const totalTime = responses.reduce((sum, r) => sum + r.timeSpent, 0);
    
    return (
      <div className="max-w-4xl mx-auto px-6 py-8">
        <div className="text-center mb-8">
          <div className="bg-gradient-to-r from-purple-500 to-indigo-600 p-6 rounded-xl shadow-lg w-fit mx-auto mb-6">
            <Target className="h-12 w-12 text-white mx-auto" />
          </div>
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            Topic Learning Complete!
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <Card>
              <CardContent className="pt-6 text-center">
                <div className="text-2xl font-bold text-purple-600">{Math.round(averageScore)}/100</div>
                <div className="text-sm text-gray-600">Average Score</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-6 text-center">
                <div className="text-2xl font-bold text-blue-600">{responses.length}/{session.questions.length}</div>
                <div className="text-sm text-gray-600">Questions Completed</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-6 text-center">
                <div className="text-2xl font-bold text-green-600">{Math.floor(totalTime / 60)}m</div>
                <div className="text-sm text-gray-600">Total Time</div>
              </CardContent>
            </Card>
          </div>
          
          {/* Learning Objectives */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Lightbulb className="h-5 w-5" />
                Learning Objectives Covered
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2">
                {session.learningObjectives.map((objective, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500 mt-1 flex-shrink-0" />
                    <span className="text-gray-700 dark:text-gray-300">{objective}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>

          <Button onClick={onBack} className="bg-purple-600 hover:bg-purple-700">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Repair Center
          </Button>
        </div>
      </div>
    );
  }

  const currentQuestion = session.questions[currentQuestionIndex];
  const isAnswered = responses.some(r => r.questionId === currentQuestion.id);

  return (
    <div className="max-w-4xl mx-auto px-6 py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <Button variant="ghost" onClick={onBack}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Repair Center
        </Button>
        <div className="flex items-center gap-4">
          <Badge variant="outline">
            Question {currentQuestionIndex + 1} of {session.questions.length}
          </Badge>
          <div className="flex items-center gap-2">
            <Brain className="h-4 w-4 text-purple-600" />
            <span className="text-sm font-medium">Topic Learning</span>
          </div>
        </div>
      </div>

      {/* Question */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BookOpen className="h-5 w-5" />
            Conceptual Question
            <Badge variant="secondary">{currentQuestion.topic}</Badge>
            <Badge className={
              currentQuestion.difficulty === 'easy' ? 'bg-green-500' :
              currentQuestion.difficulty === 'medium' ? 'bg-yellow-500' : 'bg-red-500'
            }>
              {currentQuestion.difficulty}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-lg font-medium mb-6 p-6 bg-gray-50 dark:bg-gray-800 rounded-lg">
            {currentQuestion.question}
          </div>

          <div className="mb-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
            <div className="flex items-center gap-2 mb-2">
              <Clock className="h-4 w-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-800 dark:text-blue-200">
                Estimated Time: {currentQuestion.timeEstimate}
              </span>
            </div>
            <p className="text-sm text-blue-700 dark:text-blue-300">
              Explain your understanding in detail. Focus on the key concepts and provide examples where possible.
            </p>
          </div>

          {/* Answer Input */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Your Explanation:
            </label>
            <Textarea
              value={userAnswer}
              onChange={(e) => setUserAnswer(e.target.value)}
              placeholder="Provide a detailed explanation of your understanding..."
              className="min-h-[150px]"
              disabled={isAnswered}
            />
          </div>

          {!isAnswered && (
            <Button
              onClick={handleSubmitAnswer}
              className="w-full bg-purple-600 hover:bg-purple-700"
              disabled={!userAnswer.trim() || isSubmitting || isAnalyzing}
            >
              {isSubmitting || isAnalyzing ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  {isAnalyzing ? 'Analyzing Understanding...' : 'Submitting...'}
                </>
              ) : (
                'Submit Explanation'
              )}
            </Button>
          )}
        </CardContent>
      </Card>

      {/* Navigation */}
      <div className="flex justify-between">
        <Button
          variant="outline"
          onClick={handlePreviousQuestion}
          disabled={currentQuestionIndex === 0}
        >
          <ChevronLeft className="h-4 w-4 mr-2" />
          Previous
        </Button>
        
        <Button
          variant="outline"
          onClick={handleNextQuestion}
          disabled={currentQuestionIndex === session.questions.length - 1}
        >
          Next
          <ChevronRight className="h-4 w-4 ml-2" />
        </Button>
      </div>
    </div>
  );
}
