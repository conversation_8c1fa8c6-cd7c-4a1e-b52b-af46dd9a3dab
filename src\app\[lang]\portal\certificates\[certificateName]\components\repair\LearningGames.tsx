"use client";

import React, { useState } from "react";
import { Certificate } from "@/Firebase/firestore/services/CertificatesService";
import { useToast } from "@/components/ui/use-toast";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  ArrowLeft,
  Gamepad2,
  Brain,
  Target,
  Shuffle,
  CheckCircle,
  XCircle,
  RotateCcw
} from "lucide-react";

interface LearningGamesProps {
  certificate: Certificate;
  topics: string[];
  onBack: () => void;
}

const gameTypes = [
  {
    id: 'flashcards',
    name: 'Flash Cards',
    description: 'Quick review with question and answer cards',
    icon: '🃏',
    color: 'bg-blue-500'
  },
  {
    id: 'matching',
    name: 'Matching Game',
    description: 'Match concepts with their definitions',
    icon: '🔗',
    color: 'bg-green-500'
  },
  {
    id: 'fill_blanks',
    name: 'Fill in the Blanks',
    description: 'Complete sentences with missing terms',
    icon: '📝',
    color: 'bg-purple-500'
  },
  {
    id: 'true_false',
    name: 'True or False',
    description: 'Evaluate statements for accuracy',
    icon: '✅',
    color: 'bg-orange-500'
  },
  {
    id: 'scenario_analysis',
    name: 'Scenario Analysis',
    description: 'Apply knowledge to real-world situations',
    icon: '🎭',
    color: 'bg-red-500'
  },
  {
    id: 'concept_mapping',
    name: 'Concept Mapping',
    description: 'Connect related concepts and ideas',
    icon: '🗺️',
    color: 'bg-indigo-500'
  }
];

export default function LearningGames({ certificate, topics, onBack }: LearningGamesProps) {
  const [selectedGameType, setSelectedGameType] = useState<string | null>(null);
  const [gameData, setGameData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [currentItemIndex, setCurrentItemIndex] = useState(0);
  const [showAnswer, setShowAnswer] = useState(false);
  const [score, setScore] = useState({ correct: 0, total: 0 });

  const { toast } = useToast();

  const generateGame = async (gameType: string) => {
    try {
      setIsLoading(true);
      setSelectedGameType(gameType);
      
      const response = await fetch('/api/GenerateLearningGames', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          topics,
          certificateName: certificate.name,
          gameType,
          difficulty: 'medium',
          itemCount: 10
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to generate game');
      }

      const result = await response.json();
      
      if (result.success && result.data) {
        setGameData(result.data);
        setCurrentItemIndex(0);
        setShowAnswer(false);
        setScore({ correct: 0, total: 0 });
        
        toast({
          title: "Game Ready!",
          description: `Generated ${result.data.items.length} items for ${gameTypes.find(g => g.id === gameType)?.name}.`,
        });
      } else {
        throw new Error('Invalid response format');
      }
    } catch (error) {
      console.error('Error generating game:', error);
      toast({
        title: "Error",
        description: "Failed to generate game. Please try again.",
        variant: "destructive",
      });
      setSelectedGameType(null);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAnswer = (isCorrect: boolean) => {
    setScore(prev => ({
      correct: prev.correct + (isCorrect ? 1 : 0),
      total: prev.total + 1
    }));
    setShowAnswer(true);
  };

  const nextItem = () => {
    if (currentItemIndex < gameData.items.length - 1) {
      setCurrentItemIndex(currentItemIndex + 1);
      setShowAnswer(false);
    }
  };

  const previousItem = () => {
    if (currentItemIndex > 0) {
      setCurrentItemIndex(currentItemIndex - 1);
      setShowAnswer(false);
    }
  };

  const resetGame = () => {
    setCurrentItemIndex(0);
    setShowAnswer(false);
    setScore({ correct: 0, total: 0 });
  };

  const backToGameSelection = () => {
    setSelectedGameType(null);
    setGameData(null);
    setCurrentItemIndex(0);
    setShowAnswer(false);
    setScore({ correct: 0, total: 0 });
  };

  // Game Selection Screen
  if (!selectedGameType) {
    return (
      <div className="max-w-6xl mx-auto px-6 py-8">
        <div className="flex items-center justify-between mb-8">
          <Button variant="ghost" onClick={onBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Repair Center
          </Button>
          <div className="flex items-center gap-2">
            <Gamepad2 className="h-5 w-5 text-purple-600" />
            <span className="text-lg font-semibold">Learning Games</span>
          </div>
        </div>

        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            Choose Your Learning Game
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-2">
            Practice with {topics.length} topics: {topics.join(', ')}
          </p>
          <Badge variant="outline" className="text-sm">
            {certificate.name}
          </Badge>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {gameTypes.map((game) => (
            <Card 
              key={game.id} 
              className="cursor-pointer hover:shadow-lg transition-shadow duration-200"
              onClick={() => generateGame(game.id)}
            >
              <CardHeader className="text-center">
                <div className={`${game.color} p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center`}>
                  <span className="text-2xl">{game.icon}</span>
                </div>
                <CardTitle className="text-xl">{game.name}</CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  {game.description}
                </p>
                <Button 
                  className="w-full"
                  disabled={isLoading}
                >
                  {isLoading && selectedGameType === game.id ? 'Generating...' : 'Play Game'}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  // Game Play Screen
  if (!gameData || isLoading) {
    return (
      <div className="max-w-4xl mx-auto px-6 py-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-gray-500 dark:text-gray-400">Generating your game...</p>
        </div>
      </div>
    );
  }

  const currentItem = gameData.items[currentItemIndex];
  const gameTypeInfo = gameTypes.find(g => g.id === selectedGameType);

  return (
    <div className="max-w-4xl mx-auto px-6 py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <Button variant="ghost" onClick={backToGameSelection}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Games
        </Button>
        <div className="flex items-center gap-4">
          <Badge variant="outline">
            {currentItemIndex + 1} of {gameData.items.length}
          </Badge>
          <Badge variant="outline">
            Score: {score.correct}/{score.total}
          </Badge>
          <div className="flex items-center gap-2">
            <span className="text-2xl">{gameTypeInfo?.icon}</span>
            <span className="font-semibold">{gameTypeInfo?.name}</span>
          </div>
        </div>
      </div>

      {/* Game Content */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5" />
            {gameTypeInfo?.name}
            {currentItem.topic && (
              <Badge variant="secondary">{currentItem.topic}</Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Flash Cards */}
          {selectedGameType === 'flashcards' && (
            <div className="text-center">
              <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-8 mb-6 min-h-[200px] flex items-center justify-center">
                <div>
                  <h3 className="text-lg font-semibold mb-4">
                    {showAnswer ? 'Answer:' : 'Question:'}
                  </h3>
                  <p className="text-xl">
                    {showAnswer ? currentItem.back : currentItem.front}
                  </p>
                </div>
              </div>
              
              {!showAnswer ? (
                <Button onClick={() => setShowAnswer(true)} className="bg-purple-600 hover:bg-purple-700">
                  Show Answer
                </Button>
              ) : (
                <div className="space-y-4">
                  <div className="flex gap-4 justify-center">
                    <Button onClick={() => handleAnswer(false)} variant="outline" className="border-red-500 text-red-600">
                      <XCircle className="h-4 w-4 mr-2" />
                      Didn't Know
                    </Button>
                    <Button onClick={() => handleAnswer(true)} className="bg-green-600 hover:bg-green-700">
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Got It Right
                    </Button>
                  </div>
                  {currentItem.hints && (
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      <strong>Hints:</strong> {currentItem.hints.join(', ')}
                    </div>
                  )}
                </div>
              )}
            </div>
          )}

          {/* True/False */}
          {selectedGameType === 'true_false' && (
            <div className="text-center">
              <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-8 mb-6">
                <p className="text-xl mb-6">{currentItem.statement}</p>
                
                {!showAnswer ? (
                  <div className="flex gap-4 justify-center">
                    <Button 
                      onClick={() => {
                        handleAnswer(currentItem.answer === true);
                        setShowAnswer(true);
                      }}
                      className="bg-green-600 hover:bg-green-700"
                    >
                      <CheckCircle className="h-4 w-4 mr-2" />
                      True
                    </Button>
                    <Button 
                      onClick={() => {
                        handleAnswer(currentItem.answer === false);
                        setShowAnswer(true);
                      }}
                      variant="outline" 
                      className="border-red-500 text-red-600"
                    >
                      <XCircle className="h-4 w-4 mr-2" />
                      False
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div className={`p-4 rounded-lg ${currentItem.answer ? 'bg-green-100 dark:bg-green-900/20' : 'bg-red-100 dark:bg-red-900/20'}`}>
                      <strong>Correct Answer:</strong> {currentItem.answer ? 'True' : 'False'}
                    </div>
                    <p className="text-gray-700 dark:text-gray-300">{currentItem.explanation}</p>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Fill in the Blanks */}
          {selectedGameType === 'fill_blanks' && (
            <div className="text-center">
              <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-8 mb-6">
                <p className="text-xl mb-6">{currentItem.sentence}</p>

                {!showAnswer ? (
                  <Button onClick={() => setShowAnswer(true)} className="bg-purple-600 hover:bg-purple-700">
                    Show Answers
                  </Button>
                ) : (
                  <div className="space-y-4">
                    <div className="p-4 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                      <strong>Answers:</strong> {currentItem.answers.join(', ')}
                    </div>
                    <p className="text-gray-700 dark:text-gray-300">{currentItem.explanation}</p>
                    <div className="flex gap-4 justify-center">
                      <Button onClick={() => handleAnswer(false)} variant="outline" className="border-red-500 text-red-600">
                        <XCircle className="h-4 w-4 mr-2" />
                        Didn't Know
                      </Button>
                      <Button onClick={() => handleAnswer(true)} className="bg-green-600 hover:bg-green-700">
                        <CheckCircle className="h-4 w-4 mr-2" />
                        Got It Right
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Matching Game */}
          {selectedGameType === 'matching' && (
            <div className="text-center">
              <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-8 mb-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  <div>
                    <h4 className="font-semibold mb-4">Match This:</h4>
                    <div className="p-4 bg-white dark:bg-gray-700 rounded border-2 border-purple-300">
                      {currentItem.left}
                    </div>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-4">With This:</h4>
                    <div className="p-4 bg-white dark:bg-gray-700 rounded border-2 border-blue-300">
                      {showAnswer ? currentItem.right : '???'}
                    </div>
                  </div>
                </div>

                {!showAnswer ? (
                  <Button onClick={() => setShowAnswer(true)} className="bg-purple-600 hover:bg-purple-700 mt-6">
                    Show Match
                  </Button>
                ) : (
                  <div className="space-y-4 mt-6">
                    <p className="text-gray-700 dark:text-gray-300">{currentItem.explanation}</p>
                    <div className="flex gap-4 justify-center">
                      <Button onClick={() => handleAnswer(false)} variant="outline" className="border-red-500 text-red-600">
                        <XCircle className="h-4 w-4 mr-2" />
                        Didn't Know
                      </Button>
                      <Button onClick={() => handleAnswer(true)} className="bg-green-600 hover:bg-green-700">
                        <CheckCircle className="h-4 w-4 mr-2" />
                        Got It Right
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Scenario Analysis */}
          {selectedGameType === 'scenario_analysis' && (
            <div className="text-center">
              <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-8 mb-6">
                <div className="text-left mb-6">
                  <h4 className="font-semibold mb-4">Scenario:</h4>
                  <p className="text-lg mb-4 p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded border-l-4 border-yellow-400">
                    {currentItem.scenario}
                  </p>
                  <h4 className="font-semibold mb-2">Question:</h4>
                  <p className="text-lg">{currentItem.question}</p>
                </div>

                {!showAnswer ? (
                  <Button onClick={() => setShowAnswer(true)} className="bg-purple-600 hover:bg-purple-700">
                    Show Solution
                  </Button>
                ) : (
                  <div className="space-y-4 text-left">
                    <div className="p-4 bg-green-100 dark:bg-green-900/20 rounded-lg">
                      <strong>Correct Approach:</strong>
                      <p className="mt-2">{currentItem.correctApproach}</p>
                    </div>
                    {currentItem.keyConsiderations && (
                      <div className="p-4 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                        <strong>Key Considerations:</strong>
                        <ul className="mt-2 list-disc list-inside">
                          {currentItem.keyConsiderations.map((consideration: string, index: number) => (
                            <li key={index}>{consideration}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                    <div className="flex gap-4 justify-center">
                      <Button onClick={() => handleAnswer(false)} variant="outline" className="border-red-500 text-red-600">
                        <XCircle className="h-4 w-4 mr-2" />
                        Didn't Know
                      </Button>
                      <Button onClick={() => handleAnswer(true)} className="bg-green-600 hover:bg-green-700">
                        <CheckCircle className="h-4 w-4 mr-2" />
                        Got It Right
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Concept Mapping */}
          {selectedGameType === 'concept_mapping' && (
            <div className="text-center">
              <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-8 mb-6">
                <h4 className="font-semibold mb-6">Central Concept: {currentItem.centralConcept}</h4>

                {!showAnswer ? (
                  <div>
                    <p className="mb-4">How do these concepts relate to the central concept?</p>
                    <div className="flex flex-wrap gap-2 justify-center mb-6">
                      {currentItem.relatedConcepts.map((concept: string, index: number) => (
                        <Badge key={index} variant="outline" className="text-sm p-2">
                          {concept}
                        </Badge>
                      ))}
                    </div>
                    <Button onClick={() => setShowAnswer(true)} className="bg-purple-600 hover:bg-purple-700">
                      Show Relationships
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div className="p-4 bg-blue-100 dark:bg-blue-900/20 rounded-lg text-left">
                      <strong>Relationships:</strong>
                      <ul className="mt-2 space-y-1">
                        {currentItem.relationships.map((rel: any, index: number) => (
                          <li key={index}>
                            <strong>{rel.from}</strong> {rel.relationship} <strong>{rel.to}</strong>
                          </li>
                        ))}
                      </ul>
                    </div>
                    <p className="text-gray-700 dark:text-gray-300">{currentItem.explanation}</p>
                    <div className="flex gap-4 justify-center">
                      <Button onClick={() => handleAnswer(false)} variant="outline" className="border-red-500 text-red-600">
                        <XCircle className="h-4 w-4 mr-2" />
                        Didn't Know
                      </Button>
                      <Button onClick={() => handleAnswer(true)} className="bg-green-600 hover:bg-green-700">
                        <CheckCircle className="h-4 w-4 mr-2" />
                        Got It Right
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Navigation */}
      <div className="flex justify-between items-center">
        <Button
          variant="outline"
          onClick={previousItem}
          disabled={currentItemIndex === 0}
        >
          Previous
        </Button>

        <div className="flex gap-2">
          <Button variant="outline" onClick={resetGame}>
            <RotateCcw className="h-4 w-4 mr-2" />
            Reset
          </Button>
          <Button variant="outline" onClick={() => {
            setCurrentItemIndex(Math.floor(Math.random() * gameData.items.length));
            setShowAnswer(false);
          }}>
            <Shuffle className="h-4 w-4 mr-2" />
            Random
          </Button>
        </div>

        <Button
          variant="outline"
          onClick={nextItem}
          disabled={currentItemIndex === gameData.items.length - 1}
        >
          Next
        </Button>
      </div>

      {/* Progress */}
      <div className="mt-6 text-center">
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-2">
          <div 
            className="bg-purple-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${((currentItemIndex + 1) / gameData.items.length) * 100}%` }}
          ></div>
        </div>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Progress: {currentItemIndex + 1} of {gameData.items.length} items
        </p>
      </div>
    </div>
  );
}
