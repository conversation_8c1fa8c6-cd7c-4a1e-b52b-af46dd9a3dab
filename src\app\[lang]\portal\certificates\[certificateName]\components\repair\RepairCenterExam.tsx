"use client";

import React, { useState, useEffect } from "react";
import { Certificate } from "@/Firebase/firestore/services/CertificatesService";
import { Question } from "@/Firebase/firestore/services/QuestionsService";
import {
  createRepairCenterExamAttempt,
  addQuestionsToRepairCenterExamAttempt,
  submitRepairCenterExamAnswer,
  updateQuestionExplanationAnalysis,
  getRepairCenterExamAttempt,
  getRepairCenterExamQuestions,
  completeRepairCenterExamAttempt,
  RepairCenterExamAttempt,
  RepairCenterExamQuestion
} from "@/Firebase/firestore/services/RepairCenterExamService";
import { useAuth } from "@/context";
import { useToast } from "@/components/ui/use-toast";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import {
  ArrowLeft,
  ChevronLeft,
  ChevronRight,
  CheckCircle,
  XCircle,
  Loader2,
  BookOpen,
  MessageSquare,
  Target,
  Brain
} from "lucide-react";

interface RepairCenterExamProps {
  certificate: Certificate;
  repairQuestions: Question[];
  onBack: () => void;
}

export default function RepairCenterExam({ certificate, repairQuestions, onBack }: RepairCenterExamProps) {
  const [currentAttempt, setCurrentAttempt] = useState<RepairCenterExamAttempt | null>(null);
  const [examQuestions, setExamQuestions] = useState<RepairCenterExamQuestion[]>([]);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [currentAnswer, setCurrentAnswer] = useState<'A' | 'B' | 'C' | 'D' | null>(null);
  const [userExplanation, setUserExplanation] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [showResults, setShowResults] = useState(false);

  const { user } = useAuth();
  const { toast } = useToast();

  useEffect(() => {
    startExam();
  }, []);

  const startExam = async () => {
    if (!user?.uid || !certificate.id || repairQuestions.length === 0) return;

    try {
      // Create exam attempt
      const attemptId = await createRepairCenterExamAttempt(
        user.uid,
        certificate.id,
        repairQuestions.map(q => q.id!).filter(Boolean)
      );

      // Add questions to the attempt
      const questionsForAttempt = repairQuestions.map(q => ({
        questionId: q.id!,
        questionText: q.question,
        choiceA: q.choiceA,
        choiceB: q.choiceB,
        choiceC: q.choiceC,
        choiceD: q.choiceD,
        correctAnswer: q.correctAnswer,
        topic: q.category,
        explanation: q.explanation,
      }));

      await addQuestionsToRepairCenterExamAttempt(attemptId, questionsForAttempt);

      // Load the attempt and questions
      const [attempt, questions] = await Promise.all([
        getRepairCenterExamAttempt(attemptId),
        getRepairCenterExamQuestions(attemptId)
      ]);

      if (attempt && questions.length > 0) {
        setCurrentAttempt(attempt);
        setExamQuestions(questions);
      }

      toast({
        title: "Repair Center Exam Started",
        description: `Practice exam with ${repairQuestions.length} questions from your repair center.`,
      });

    } catch (error) {
      console.error('Error starting repair center exam:', error);
      toast({
        title: "Error",
        description: "Failed to start exam. Please try again.",
        variant: "destructive",
      });
      onBack();
    }
  };

  const analyzeExplanation = async (question: RepairCenterExamQuestion, answer: 'A' | 'B' | 'C' | 'D', explanation: string) => {
    try {
      setIsAnalyzing(true);
      
      const response = await fetch('/api/AnalyzeUserExplanation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          question: question.questionText,
          choiceA: question.choiceA,
          choiceB: question.choiceB,
          choiceC: question.choiceC,
          choiceD: question.choiceD,
          correctAnswer: question.correctAnswer,
          userExplanation: explanation,
          topic: question.topic || 'General',
          certificateName: certificate.name
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to analyze explanation');
      }

      const result = await response.json();
      
      if (result.success && result.data) {
        // Update the question with analysis
        await updateQuestionExplanationAnalysis(
          currentAttempt!.id!,
          question.questionId,
          result.data
        );
        
        return result.data;
      } else {
        throw new Error('Invalid response format');
      }
    } catch (error) {
      console.error('Error analyzing explanation:', error);
      toast({
        title: "Analysis Error",
        description: "Could not analyze your explanation. Please try again.",
        variant: "destructive",
      });
      return null;
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleSubmitAnswer = async () => {
    if (!currentAnswer || !userExplanation.trim() || !currentAttempt?.id) {
      toast({
        title: "Incomplete Answer",
        description: "Please select an answer and provide an explanation.",
        variant: "destructive",
      });
      return;
    }

    const currentQuestion = examQuestions[currentQuestionIndex];
    if (!currentQuestion) return;

    try {
      setIsSubmitting(true);

      // Submit answer
      const { isCorrect } = await submitRepairCenterExamAnswer(
        currentAttempt.id,
        currentQuestion.questionId,
        currentAnswer,
        userExplanation,
        60 // Default time spent
      );

      // Analyze explanation
      const analysis = await analyzeExplanation(currentQuestion, currentAnswer, userExplanation);

      // Update local state
      setExamQuestions(prev => prev.map(q =>
        q.questionId === currentQuestion.questionId
          ? { 
              ...q, 
              userAnswer: currentAnswer, 
              userExplanation,
              isCorrect,
              explanationAnalysis: analysis
            }
          : q
      ));

      // Show feedback
      if (isCorrect && analysis?.isCorrect) {
        toast({
          title: "Excellent!",
          description: "Correct answer with good understanding!",
        });
      } else if (isCorrect && !analysis?.isCorrect) {
        toast({
          title: "Correct Answer",
          description: "Right answer, but your explanation could be improved.",
          variant: "default",
        });
      } else {
        toast({
          title: "Keep Learning",
          description: "Review the feedback to improve your understanding.",
          variant: "destructive",
        });
      }

      // Move to next question or show results
      setTimeout(() => {
        if (currentQuestionIndex < examQuestions.length - 1) {
          handleNextQuestion();
        } else {
          handleCompleteExam();
        }
      }, 2000);

    } catch (error) {
      console.error('Error submitting answer:', error);
      toast({
        title: "Error",
        description: "Failed to submit answer. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleNextQuestion = () => {
    if (currentQuestionIndex < examQuestions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
      setCurrentAnswer(null);
      setUserExplanation('');
    }
  };

  const handlePreviousQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(currentQuestionIndex - 1);
      setCurrentAnswer(null);
      setUserExplanation('');
    }
  };

  const handleCompleteExam = async () => {
    if (!currentAttempt?.id) return;

    try {
      await completeRepairCenterExamAttempt(currentAttempt.id);
      setShowResults(true);

      toast({
        title: "Exam Completed!",
        description: "Review your performance and explanations below.",
      });
    } catch (error) {
      console.error('Error completing exam:', error);
      toast({
        title: "Error",
        description: "Failed to complete exam. Please try again.",
        variant: "destructive",
      });
    }
  };

  if (!currentAttempt || examQuestions.length === 0) {
    return (
      <div className="max-w-4xl mx-auto px-6 py-8">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-purple-600 mx-auto mb-4" />
          <p className="text-gray-500 dark:text-gray-400">Starting your repair center exam...</p>
        </div>
      </div>
    );
  }

  if (showResults) {
    const correctAnswers = examQuestions.filter(q => q.isCorrect).length;
    const goodExplanations = examQuestions.filter(q => q.explanationAnalysis?.isCorrect).length;
    
    return (
      <div className="max-w-4xl mx-auto px-6 py-8">
        <div className="text-center mb-8">
          <div className="bg-gradient-to-r from-purple-500 to-indigo-600 p-6 rounded-xl shadow-lg w-fit mx-auto mb-6">
            <Target className="h-12 w-12 text-white mx-auto" />
          </div>
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            Repair Center Exam Complete!
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <Card>
              <CardContent className="pt-6 text-center">
                <div className="text-2xl font-bold text-green-600">{correctAnswers}/{examQuestions.length}</div>
                <div className="text-sm text-gray-600">Correct Answers</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-6 text-center">
                <div className="text-2xl font-bold text-blue-600">{goodExplanations}/{examQuestions.length}</div>
                <div className="text-sm text-gray-600">Good Explanations</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-6 text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {Math.round(((correctAnswers + goodExplanations) / (examQuestions.length * 2)) * 100)}%
                </div>
                <div className="text-sm text-gray-600">Overall Score</div>
              </CardContent>
            </Card>
          </div>
          <Button onClick={onBack} className="bg-purple-600 hover:bg-purple-700">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Repair Center
          </Button>
        </div>
      </div>
    );
  }

  const currentQuestion = examQuestions[currentQuestionIndex];
  const isAnswered = currentQuestion.userAnswer !== undefined;

  return (
    <div className="max-w-4xl mx-auto px-6 py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <Button variant="ghost" onClick={onBack}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Repair Center
        </Button>
        <div className="flex items-center gap-4">
          <Badge variant="outline">
            Question {currentQuestionIndex + 1} of {examQuestions.length}
          </Badge>
          <div className="flex items-center gap-2">
            <Brain className="h-4 w-4 text-purple-600" />
            <span className="text-sm font-medium">Repair Center Exam</span>
          </div>
        </div>
      </div>

      {/* Question */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            Question {currentQuestionIndex + 1}
            {currentQuestion.topic && (
              <Badge variant="secondary">{currentQuestion.topic}</Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-lg font-medium mb-6 p-6 bg-gray-50 dark:bg-gray-800 rounded-lg">
            {currentQuestion.questionText}
          </div>

          <div className="grid grid-cols-1 gap-3 mb-6">
            {(['A', 'B', 'C', 'D'] as const).map((choice) => {
              const isSelected = currentAnswer === choice;
              
              return (
                <Button
                  key={choice}
                  variant="outline"
                  className={`text-left justify-start p-4 h-auto ${
                    isSelected ? 'bg-purple-100 dark:bg-purple-900/30 border-purple-500' : ''
                  }`}
                  onClick={() => !isAnswered && setCurrentAnswer(choice)}
                  disabled={isAnswered}
                >
                  <span className="font-bold mr-3">{choice}.</span>
                  <span>{currentQuestion[`choice${choice}` as keyof RepairCenterExamQuestion] as string}</span>
                </Button>
              );
            })}
          </div>

          {/* Explanation Input */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Explain why you think this is the correct answer:
            </label>
            <Textarea
              value={userExplanation}
              onChange={(e) => setUserExplanation(e.target.value)}
              placeholder="Provide a detailed explanation of your reasoning..."
              className="min-h-[120px]"
              disabled={isAnswered}
            />
          </div>

          {!isAnswered && (
            <Button
              onClick={handleSubmitAnswer}
              className="w-full bg-purple-600 hover:bg-purple-700"
              disabled={!currentAnswer || !userExplanation.trim() || isSubmitting || isAnalyzing}
            >
              {isSubmitting || isAnalyzing ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  {isAnalyzing ? 'Analyzing Explanation...' : 'Submitting...'}
                </>
              ) : (
                'Submit Answer & Explanation'
              )}
            </Button>
          )}
        </CardContent>
      </Card>

      {/* Navigation */}
      <div className="flex justify-between">
        <Button
          variant="outline"
          onClick={handlePreviousQuestion}
          disabled={currentQuestionIndex === 0}
        >
          <ChevronLeft className="h-4 w-4 mr-2" />
          Previous
        </Button>
        
        <Button
          variant="outline"
          onClick={handleNextQuestion}
          disabled={currentQuestionIndex === examQuestions.length - 1}
        >
          Next
          <ChevronRight className="h-4 w-4 ml-2" />
        </Button>
      </div>
    </div>
  );
}
